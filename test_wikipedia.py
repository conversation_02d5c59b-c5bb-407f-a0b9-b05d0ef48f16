#!/usr/bin/env python3

import wikipediaapi

def test_wikipedia():
    print("Testing Wikipedia API...")
    
    try:
        # Initialize Wikipedia API
        wiki = wikipediaapi.Wikipedia(
            language='en',
            extract_format=wikipediaapi.ExtractFormat.WIKI,
            user_agent='VQAEnhancer/1.0 (<EMAIL>)'
        )
        print("✓ Wikipedia API initialized")
        
        # Test with a simple page
        test_keywords = ['Korea', 'Jeju', 'Seoul', 'Python']
        
        for keyword in test_keywords:
            print(f"\nTesting keyword: {keyword}")
            try:
                page = wiki.page(keyword)
                print(f"  Page object created")
                
                exists = page.exists()
                print(f"  Page exists: {exists}")
                
                if exists:
                    title = page.title
                    print(f"  Title: {title}")
                    
                    summary = page.summary
                    if summary:
                        print(f"  Summary length: {len(summary)}")
                        print(f"  First 100 chars: {summary[:100]}...")
                        
                        url = page.fullurl
                        print(f"  URL: {url}")
                    else:
                        print("  No summary available")
                else:
                    print("  Page does not exist")
                    
            except Exception as e:
                print(f"  Error with {keyword}: {e}")
        
        print("\nWikipedia API test completed!")
        
    except Exception as e:
        print(f"Failed to initialize Wikipedia API: {e}")

if __name__ == "__main__":
    test_wikipedia()
