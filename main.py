import pandas as pd
import wikipediaapi
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import re
import time
from typing import Tuple, Optional

class VQAEnhancer:
    def __init__(self):
        """Initialize the VQA enhancer with Llama model and Wikipedia API."""
        print("Initializing VQA Enhancer...")

        # Initialize Wikipedia API
        self.wiki = wikipediaapi.Wikipedia(
            language='en',
            extract_format=wikipediaapi.ExtractFormat.WIKI,
            user_agent='VQAEnhancer/1.0 (https://example.com/contact)'
        )

        # Initialize Llama 3.1 8B Instruct model
        print("Loading Llama 3.1 8B Instruct model...")
        model_name = "meta-llama/Meta-Llama-3.1-8B-Instruct"

        try:
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
            print("Model loaded successfully!")
        except Exception as e:
            print(f"Error loading model: {e}")
            print("Falling back to a smaller model...")
            # Fallback to a smaller model if Llama 3.1 8B is not available
            model_name = "microsoft/DialoGPT-medium"
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForCausalLM.from_pretrained(model_name)

        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

    def generate_keyword(self, question: str, correct_option: str) -> str:
        """Generate a keyword for Wikipedia search using Llama model."""
        prompt = f"""Based on the following question and correct answer, generate a single, specific keyword or short phrase (2-4 words maximum) that would be best for searching Wikipedia to find relevant background information. The keyword should be in English and refer to a specific place, concept, historical event, or cultural element.

Question: {question}
Correct Answer: {correct_option}

Keyword:"""

        try:
            # Tokenize input
            inputs = self.tokenizer.encode(prompt, return_tensors="pt", truncation=True, max_length=512)

            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_new_tokens=20,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )

            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

            # Extract keyword from response
            keyword = response.split("Keyword:")[-1].strip()

            # Clean up the keyword
            keyword = re.sub(r'[^\w\s-]', '', keyword)
            keyword = keyword.strip()

            # If keyword is too long or empty, extract key terms from question
            if len(keyword) > 50 or not keyword:
                keyword = self.extract_key_terms(question, correct_option)

            return keyword

        except Exception as e:
            print(f"Error generating keyword: {e}")
            return self.extract_key_terms(question, correct_option)

    def extract_key_terms(self, question: str, correct_option: str) -> str:
        """Fallback method to extract key terms from question and answer."""
        # Combine question and correct option
        text = f"{question} {correct_option}"

        # Remove common words and extract meaningful terms
        common_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'this', 'that', 'these', 'those', 'it', 'its', 'how', 'what', 'why', 'when', 'where', 'which', 'who'}

        # Extract words that might be proper nouns or important terms
        words = re.findall(r'\b[A-Z][a-z]+\b|\b[a-z]{4,}\b', text)
        keywords = [word for word in words if word.lower() not in common_words]

        # Return first few meaningful words
        return ' '.join(keywords[:3]) if keywords else "Korean culture"

    def search_wikipedia(self, keyword: str) -> Tuple[Optional[str], Optional[str]]:
        """Search Wikipedia and return first paragraph and URL."""
        try:
            # Search for the page
            page = self.wiki.page(keyword)

            if page.exists():
                # Get the first paragraph (summary)
                summary = page.summary
                if summary:
                    # Get first paragraph (split by double newline or first few sentences)
                    first_paragraph = summary.split('\n\n')[0]
                    if len(first_paragraph) > 500:
                        # If too long, take first 2-3 sentences
                        sentences = first_paragraph.split('. ')
                        first_paragraph = '. '.join(sentences[:3]) + '.'

                    return first_paragraph, page.fullurl

            # If exact match doesn't work, try search
            search_results = self.wiki.search(keyword, results=5)
            for result in search_results:
                page = self.wiki.page(result)
                if page.exists():
                    summary = page.summary
                    if summary:
                        first_paragraph = summary.split('\n\n')[0]
                        if len(first_paragraph) > 500:
                            sentences = first_paragraph.split('. ')
                            first_paragraph = '. '.join(sentences[:3]) + '.'
                        return first_paragraph, page.fullurl

            return None, None

        except Exception as e:
            print(f"Error searching Wikipedia for '{keyword}': {e}")
            return None, None

    def process_row(self, row: pd.Series) -> Tuple[str, str, str]:
        """Process a single row and return keyword, knowledge point, and source."""
        question = row['Question']

        # Get correct option text
        correct_option_num = row['Correct Option']
        correct_option = row[f'Option {correct_option_num}']

        print(f"Processing: {question[:50]}...")

        # Generate keyword using Llama model
        keyword = self.generate_keyword(question, correct_option)
        print(f"Generated keyword: {keyword}")

        # Search Wikipedia
        knowledge_point, source_url = self.search_wikipedia(keyword)

        if knowledge_point is None:
            # Fallback: try with a simpler keyword
            simple_keyword = keyword.split()[0] if keyword else "Korea"
            knowledge_point, source_url = self.search_wikipedia(simple_keyword)

        if knowledge_point is None:
            knowledge_point = "No relevant Wikipedia information found."
            source_url = "N/A"

        print(f"Found knowledge point: {knowledge_point[:100]}...")

        return keyword, knowledge_point, source_url

    def enhance_csv(self, input_file: str, output_file: str = None):
        """Enhance the CSV file with new columns."""
        if output_file is None:
            output_file = input_file.replace('.csv', '_enhanced.csv')

        print(f"Reading CSV file: {input_file}")
        df = pd.read_csv(input_file)

        print(f"Found {len(df)} rows to process")

        # Initialize new columns
        keywords = []
        knowledge_points = []
        sources = []

        # Process each row
        for idx, row in df.iterrows():
            try:
                keyword, knowledge_point, source = self.process_row(row)
                keywords.append(keyword)
                knowledge_points.append(knowledge_point)
                sources.append(source)

                print(f"Completed row {idx + 1}/{len(df)}")

                # Add small delay to avoid overwhelming APIs
                time.sleep(0.5)

            except Exception as e:
                print(f"Error processing row {idx + 1}: {e}")
                keywords.append("Error")
                knowledge_points.append("Error processing this row")
                sources.append("N/A")

        # Add new columns to dataframe
        df['keyword'] = keywords
        df['knowledge point'] = knowledge_points
        df['knowledge point source'] = sources

        # Save enhanced CSV
        print(f"Saving enhanced CSV to: {output_file}")
        df.to_csv(output_file, index=False)
        print("Enhancement complete!")

def main():
    """Main function to run the VQA enhancement."""
    enhancer = VQAEnhancer()
    enhancer.enhance_csv('VQA.csv')

if __name__ == "__main__":
    main()