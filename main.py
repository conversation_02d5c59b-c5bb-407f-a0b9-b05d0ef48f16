import csv
import wikipediaapi
import torch
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM
import re
import time
from typing import Tuple, Optional, List, Dict

class VQAEnhancer:
    def __init__(self):
        """Initialize the VQA enhancer with Llama model and Wikipedia API."""
        print("Initializing VQA Enhancer...")

        # Initialize Wikipedia API
        self.wiki = wikipediaapi.Wikipedia(
            language='en',
            extract_format=wikipediaapi.ExtractFormat.WIKI,
            user_agent='VQAEnhancer/1.0 (https://example.com/contact)'
        )

        # Initialize Llama 3.1 8B Instruct model
        print("Loading Llama 3.1 8B Instruct model...")

        # Try different model names in order of preference
        model_names = [
            "meta-llama/Meta-Llama-3.1-8B-Instruct",
            "meta-llama/Llama-3.1-8B-Instruct",
            "microsoft/DialoGPT-medium",
            "gpt2"
        ]

        self.model = None
        self.tokenizer = None

        for model_name in model_names:
            try:
                print(f"Trying to load: {model_name}")
                self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_name,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                    device_map="auto" if torch.cuda.is_available() else None,
                    trust_remote_code=True
                )
                print(f"Successfully loaded: {model_name}")
                break
            except Exception as e:
                print(f"Failed to load {model_name}: {e}")
                continue

        if self.model is None:
            print("Warning: Could not load any language model. Using simple keyword extraction.")
            self.use_llm = False
        else:
            self.use_llm = True

        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

    def generate_keyword(self, question: str, correct_option: str, existing_keyword: str = "") -> str:
        """Generate a keyword for Wikipedia search using Llama model or fallback method."""

        # If no LLM is available, use simple extraction
        if not self.use_llm:
            return self.extract_key_terms(question, correct_option, existing_keyword)

        prompt = f"""Based on the following question and correct answer, generate a single, specific keyword or short phrase (2-4 words maximum) that would be best for searching Wikipedia to find relevant background information. The keyword should be in English and refer to a specific place, concept, historical event, or cultural element.

Question: {question}
Correct Answer: {correct_option}

Keyword:"""

        try:
            # Tokenize input
            inputs = self.tokenizer.encode(prompt, return_tensors="pt", truncation=True, max_length=512)

            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_new_tokens=20,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )

            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

            # Extract keyword from response
            keyword = response.split("Keyword:")[-1].strip()

            # Clean up the keyword
            keyword = re.sub(r'[^\w\s-]', '', keyword)
            keyword = keyword.strip()

            # If keyword is too long or empty, extract key terms from question
            if len(keyword) > 50 or not keyword:
                keyword = self.extract_key_terms(question, correct_option, existing_keyword)

            return keyword

        except Exception as e:
            print(f"Error generating keyword: {e}")
            return self.extract_key_terms(question, correct_option, existing_keyword)

    def extract_key_terms(self, question: str, correct_option: str, existing_keyword: str = "") -> str:
        """Fallback method to extract key terms from question and answer."""

        # First, check if we have an existing Korean keyword that might be useful
        if existing_keyword and existing_keyword != "":
            # Try to translate or find English equivalent of Korean terms
            korean_to_english = {
                '제주': 'Jeju Island',
                '돌집': 'Jeju stone house',
                '월정교': 'Woljeong Bridge',
                '운현궁': 'Unhyeongung Palace',
                '명동': 'Myeongdong',
                '남산타워': 'Namsan Tower',
                '신라대종': 'Divine Bell of King Seongdeok',
                '고려대학교': 'Korea University',
                '한강다리': 'Han River',
                '탑골공원': 'Tapgol Park',
                '광화문': 'Gwanghwamun',
                '경복궁': 'Gyeongbokgung',
                '창덕궁': 'Changdeokgung',
                '덕수궁': 'Deoksugung',
                '종묘': 'Jongmyo',
                '독립문': 'Independence Gate',
                '불국사': 'Bulguksa',
                '첨성대': 'Cheomseongdae',
                '수원화성': 'Hwaseong Fortress',
                '해동용궁사': 'Haedong Yonggungsa'
            }

            for korean, english in korean_to_english.items():
                if korean in existing_keyword:
                    return english

        # Combine question and correct option
        text = f"{question} {correct_option}"

        # Remove common words and extract meaningful terms
        common_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'this', 'that', 'these', 'those', 'it', 'its', 'how', 'what', 'why', 'when', 'where', 'which', 'who', 'building', 'structure', 'design', 'style', 'features', 'elements', 'aspects', 'factors'}

        # Look for specific Korean cultural terms first
        korean_terms = re.findall(r'\b(?:Korean|Korea|Silla|Joseon|Seoul|Busan|Gyeongju|palace|temple|gate|tower|bridge|market|village|fortress|shrine|observatory)\b', text, re.IGNORECASE)

        # Extract words that might be proper nouns or important terms
        words = re.findall(r'\b[A-Z][a-z]+\b|\b[a-z]{4,}\b', text)
        keywords = [word for word in words if word.lower() not in common_words]

        # Combine and prioritize
        all_keywords = korean_terms + keywords

        # Return the most relevant keywords
        if korean_terms:
            return ' '.join(korean_terms[:2])
        elif keywords:
            return ' '.join(keywords[:2])
        else:
            return "Korean culture"

    def search_wikipedia(self, keyword: str) -> Tuple[Optional[str], Optional[str]]:
        """Search Wikipedia and return first paragraph and URL."""
        try:
            # Search for the page
            page = self.wiki.page(keyword)

            if page.exists():
                # Get the first paragraph (summary)
                summary = page.summary
                if summary:
                    # Get first paragraph (split by double newline or first few sentences)
                    first_paragraph = summary.split('\n\n')[0]
                    if len(first_paragraph) > 500:
                        # If too long, take first 2-3 sentences
                        sentences = first_paragraph.split('. ')
                        first_paragraph = '. '.join(sentences[:3]) + '.'

                    return first_paragraph, page.fullurl

            # If exact match doesn't work, try search
            search_results = self.wiki.search(keyword, results=5)
            for result in search_results:
                page = self.wiki.page(result)
                if page.exists():
                    summary = page.summary
                    if summary:
                        first_paragraph = summary.split('\n\n')[0]
                        if len(first_paragraph) > 500:
                            sentences = first_paragraph.split('. ')
                            first_paragraph = '. '.join(sentences[:3]) + '.'
                        return first_paragraph, page.fullurl

            return None, None

        except Exception as e:
            print(f"Error searching Wikipedia for '{keyword}': {e}")
            return None, None

    def process_row(self, row: Dict[str, str]) -> Tuple[str, str, str]:
        """Process a single row and return keyword, knowledge point, and source."""
        question = row['Question']

        # Get correct option text
        correct_option_num = int(row['Correct Option'])
        correct_option = row[f'Option {correct_option_num}']

        # Get existing Korean keyword if available
        existing_keyword = row.get('Keyword/Concept', '')

        print(f"Processing: {question[:50]}...")
        print(f"  Existing keyword: {existing_keyword}")

        # Generate keyword using Llama model or fallback
        keyword = self.generate_keyword(question, correct_option, existing_keyword)
        print(f"  Generated keyword: {keyword}")

        # Search Wikipedia
        knowledge_point, source_url = self.search_wikipedia(keyword)

        if knowledge_point is None:
            # Fallback: try with a simpler keyword
            simple_keyword = keyword.split()[0] if keyword else "Korea"
            print(f"  Trying fallback keyword: {simple_keyword}")
            knowledge_point, source_url = self.search_wikipedia(simple_keyword)

        if knowledge_point is None:
            knowledge_point = "No relevant Wikipedia information found."
            source_url = "N/A"

        print(f"  Found knowledge point: {knowledge_point[:100]}...")

        return keyword, knowledge_point, source_url

    def enhance_csv(self, input_file: str, output_file: str = None):
        """Enhance the CSV file with new columns."""
        if output_file is None:
            output_file = input_file.replace('.csv', '_enhanced.csv')

        print(f"Reading CSV file: {input_file}")

        # Read CSV file
        rows = []
        with open(input_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            fieldnames = reader.fieldnames
            for row in reader:
                rows.append(row)

        print(f"Found {len(rows)} rows to process")

        # Process each row
        for idx, row in enumerate(rows):
            try:
                keyword, knowledge_point, source = self.process_row(row)
                row['keyword'] = keyword
                row['knowledge point'] = knowledge_point
                row['knowledge point source'] = source

                print(f"Completed row {idx + 1}/{len(rows)}")

                # Add small delay to avoid overwhelming APIs
                time.sleep(0.5)

            except Exception as e:
                print(f"Error processing row {idx + 1}: {e}")
                row['keyword'] = "Error"
                row['knowledge point'] = "Error processing this row"
                row['knowledge point source'] = "N/A"

        # Save enhanced CSV
        print(f"Saving enhanced CSV to: {output_file}")
        new_fieldnames = fieldnames + ['keyword', 'knowledge point', 'knowledge point source']

        with open(output_file, 'w', encoding='utf-8', newline='') as file:
            writer = csv.DictWriter(file, fieldnames=new_fieldnames)
            writer.writeheader()
            writer.writerows(rows)

        print("Enhancement complete!")

def main():
    """Main function to run the VQA enhancement."""
    enhancer = VQAEnhancer()
    enhancer.enhance_csv('VQA.csv')

if __name__ == "__main__":
    main()