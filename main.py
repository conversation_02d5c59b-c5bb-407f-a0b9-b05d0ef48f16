#!/usr/bin/env python3

import csv
import wikipediaapi
import re
import time
from typing import Tuple, Optional

class SimpleVQAEnhancer:
    def __init__(self):
        """Initialize the simple VQA enhancer with Wikipedia API only."""
        print("Initializing Simple VQA Enhancer...")

        # Initialize Wikipedia API
        self.wiki = wikipediaapi.Wikipedia(
            language='en',
            extract_format=wikipediaapi.ExtractFormat.WIKI,
            user_agent='VQAEnhancer/1.0 (<EMAIL>)'
        )
        print("Wikipedia API initialized!")

    def extract_keywords_from_text(self, question: str, correct_option: str, existing_keyword: str = "") -> str:
        """Extract keywords from question and correct option using simple text processing."""

        # First, check if we have an existing Korean keyword that might be useful
        if existing_keyword and existing_keyword != "":
            # Try to translate or find English equivalent of Korean terms
            korean_to_english = {
                '제주': 'Jeju Island',
                '돌집': 'Jeju stone house',
                '월정교': 'Woljeong Bridge',
                '운현궁': 'Unhyeongung Palace',
                '명동': 'Myeongdong',
                '남산타워': 'Namsan Tower',
                '신라대종': 'Divine Bell of King Seongdeok',
                '고려대학교': 'Korea University',
                '한강다리': 'Han River',
                '탑골공원': 'Tapgol Park',
                '광화문': 'Gwanghwamun',
                '경복궁': 'Gyeongbokgung',
                '창덕궁': 'Changdeokgung',
                '덕수궁': 'Deoksugung',
                '종묘': 'Jongmyo',
                '독립문': 'Independence Gate',
                '불국사': 'Bulguksa',
                '첨성대': 'Cheomseongdae',
                '수원화성': 'Hwaseong Fortress',
                '해동용궁사': 'Haedong Yonggungsa'
            }

            for korean, english in korean_to_english.items():
                if korean in existing_keyword:
                    return english

        # Combine question and correct option
        text = f"{question} {correct_option}"

        # Remove common words and extract meaningful terms
        common_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'this', 'that', 'these', 'those', 'it', 'its', 'how', 'what', 'why', 'when', 'where', 'which', 'who', 'building', 'structure', 'design', 'style', 'features', 'elements', 'aspects', 'factors'}

        # Look for specific Korean cultural terms first
        korean_terms = re.findall(r'\b(?:Korean|Korea|Silla|Joseon|Seoul|Busan|Gyeongju|palace|temple|gate|tower|bridge|market|village|fortress|shrine|observatory)\b', text, re.IGNORECASE)

        # Extract words that might be proper nouns or important terms
        words = re.findall(r'\b[A-Z][a-z]+\b|\b[a-z]{4,}\b', text)
        keywords = [word for word in words if word.lower() not in common_words]

        # Combine and prioritize
        all_keywords = korean_terms + keywords

        # Return the most relevant keywords
        if korean_terms:
            return ' '.join(korean_terms[:2])
        elif keywords:
            return ' '.join(keywords[:2])
        else:
            return "Korean culture"

    def search_wikipedia(self, keyword: str) -> Tuple[Optional[str], Optional[str]]:
        """Search Wikipedia and return first paragraph and URL."""
        try:
            print(f"  Searching Wikipedia for: {keyword}")

            # Try exact match first
            page = self.wiki.page(keyword)

            if page.exists():
                summary = page.summary
                if summary and len(summary.strip()) > 20:  # Make sure it's not just a disambiguation
                    # Get first paragraph
                    first_paragraph = summary.split('\n\n')[0]
                    if len(first_paragraph) > 500:
                        # If too long, take first 2-3 sentences
                        sentences = first_paragraph.split('. ')
                        first_paragraph = '. '.join(sentences[:3]) + '.'

                    return first_paragraph, page.fullurl

            # If exact match doesn't work, try with modified keywords
            # Try individual words from the keyword
            words = keyword.split()
            for word in words:
                if len(word) > 3:  # Skip short words
                    page = self.wiki.page(word)
                    if page.exists():
                        summary = page.summary
                        if summary:
                            first_paragraph = summary.split('\n\n')[0]
                            if len(first_paragraph) > 500:
                                sentences = first_paragraph.split('. ')
                                first_paragraph = '. '.join(sentences[:3]) + '.'
                            return first_paragraph, page.fullurl

            return None, None

        except Exception as e:
            print(f"  Error searching Wikipedia for '{keyword}': {e}")
            return None, None

    def is_relevant_content(self, content: str, question: str, correct_option: str) -> bool:
        """Check if the Wikipedia content is relevant to the question."""
        # Simple relevance check
        content_lower = content.lower()
        question_lower = question.lower()
        option_lower = correct_option.lower()

        # Check for Korean-related terms
        korean_terms = ['korea', 'korean', 'seoul', 'silla', 'joseon', 'jeju', 'busan', 'gyeongju']
        has_korean_context = any(term in content_lower for term in korean_terms)

        # Check if it's just a disambiguation page
        is_disambiguation = 'may refer to:' in content_lower or len(content.strip()) < 50

        # For Korean cultural content, if it has Korean context and isn't disambiguation, it's relevant
        # We're being more permissive since this is a Korean cultural dataset
        return has_korean_context and not is_disambiguation

    def is_specific_enough_keyword(self, keyword: str) -> bool:
        """Check if a keyword is specific enough (not too generic)."""
        generic_keywords = {
            "korea", "korean", "korean culture", "korean architecture",
            "korean tradition", "korean history", "korean art", "culture",
            "architecture", "tradition", "history", "art", "building",
            "structure", "design", "style", "korean bridge", "korean palace",
            "korean temple", "korean tower"
        }
        return keyword.lower().strip() not in generic_keywords and len(keyword.strip()) > 0

    def search_wikipedia_iterative(self, question: str, correct_option: str, existing_keyword: str = "") -> Tuple[str, str, str]:
        """Search Wikipedia iteratively, trying different keywords until a page is found."""

        attempted_keywords = []
        max_attempts = 3  # Limited to 3 attempts as requested

        # First try the mapped keyword from Korean
        if existing_keyword:
            mapped_keyword = self.extract_keywords_from_text(question, correct_option, existing_keyword)
            if mapped_keyword != "Korean culture" and self.is_specific_enough_keyword(mapped_keyword):
                print(f"    Trying mapped keyword: {mapped_keyword}")
                knowledge_point, source_url = self.search_wikipedia(mapped_keyword)
                if knowledge_point and self.is_relevant_content(knowledge_point, question, correct_option):
                    return mapped_keyword, knowledge_point, source_url
                elif knowledge_point:
                    print(f"      Found page but content not relevant, continuing search...")
                attempted_keywords.append(mapped_keyword)

        # Try different keyword generation strategies
        for attempt in range(max_attempts):
            if attempt == 0:
                # Try to extract specific proper nouns
                proper_nouns = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', f"{question} {correct_option}")
                keyword = proper_nouns[0] if proper_nouns else ""
            elif attempt == 1:
                # Try specific Korean cultural artifacts
                if 'bell' in question.lower() or 'bell' in correct_option.lower():
                    keyword = "Emille Bell"  # Famous Korean bell
                elif 'silla' in question.lower() or 'silla' in correct_option.lower():
                    keyword = "Silla"
                else:
                    # Try to find specific place names
                    places = re.findall(r'\b(?:Seoul|Busan|Gyeongju|Jeju)\b', f"{question} {correct_option}", re.IGNORECASE)
                    keyword = places[0] if places else ""
            elif attempt == 2:
                # Try to find specific cultural terms without "Korean" prefix
                cultural_terms = re.findall(r'\b(?:palace|temple|gate|tower|bridge|university|park|shrine|fortress|observatory)\b', f"{question} {correct_option}", re.IGNORECASE)
                keyword = cultural_terms[0] if cultural_terms else ""
            else:
                break

            # Skip if keyword is not specific enough or already attempted
            if not self.is_specific_enough_keyword(keyword) or keyword in attempted_keywords or not keyword.strip():
                continue

            print(f"    Attempt {attempt + 1}: Trying keyword '{keyword}'")
            attempted_keywords.append(keyword)

            knowledge_point, source_url = self.search_wikipedia(keyword)
            if knowledge_point and self.is_relevant_content(knowledge_point, question, correct_option):
                return keyword, knowledge_point, source_url
            elif knowledge_point:
                print(f"      Found page but content not relevant, continuing search...")

        # If all attempts fail, return empty values
        print(f"    No specific enough keyword found after {max_attempts} attempts, leaving blank")
        return "", "", ""

    def process_row(self, row: dict) -> Tuple[str, str, str]:
        """Process a single row and return keyword, knowledge point, and source."""
        question = row['Question']

        # Get correct option text
        correct_option_num = int(row['Correct Option'])
        correct_option = row[f'Option {correct_option_num}']

        # Get existing Korean keyword if available
        existing_keyword = row.get('Keyword/Concept', '')

        print(f"Processing: {question[:50]}...")
        print(f"  Existing keyword: {existing_keyword}")

        # Use iterative search to find the best Wikipedia page
        keyword, knowledge_point, source_url = self.search_wikipedia_iterative(
            question, correct_option, existing_keyword
        )

        print(f"  Final keyword: {keyword if keyword else '(blank)'}")
        print(f"  Found knowledge point: {knowledge_point[:100] if knowledge_point else '(blank)'}...")

        return keyword, knowledge_point, source_url

    def enhance_csv(self, input_file: str, output_file: str = None, max_rows: int = None):
        """Enhance the CSV file with new columns."""
        if output_file is None:
            output_file = input_file.replace('.csv', '_enhanced.csv')

        print(f"Reading CSV file: {input_file}")

        # Read CSV file
        rows = []
        with open(input_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            fieldnames = reader.fieldnames
            for idx, row in enumerate(reader):
                rows.append(row)
                if max_rows and idx + 1 >= max_rows:
                    break

        total_rows = len(rows)
        print(f"Found {total_rows} rows to process")

        # Process each row
        for idx, row in enumerate(rows):
            try:
                keyword, knowledge_point, source = self.process_row(row)
                row['keyword'] = keyword
                row['knowledge point'] = knowledge_point
                row['knowledge point source'] = source

                print(f"Completed row {idx + 1}/{total_rows}")

                # Add small delay to avoid overwhelming APIs
                time.sleep(0.5)

            except Exception as e:
                print(f"Error processing row {idx + 1}: {e}")
                row['keyword'] = ""
                row['knowledge point'] = ""
                row['knowledge point source'] = ""

        # Save enhanced CSV
        print(f"Saving enhanced CSV to: {output_file}")
        new_fieldnames = list(fieldnames) + ['keyword', 'knowledge point', 'knowledge point source']

        with open(output_file, 'w', encoding='utf-8', newline='') as file:
            writer = csv.DictWriter(file, fieldnames=new_fieldnames)
            writer.writeheader()
            writer.writerows(rows)

        print("Enhancement complete!")

def main():
    """Main function to run the simple VQA enhancement."""
    enhancer = SimpleVQAEnhancer()

    # Process all rows with the improved iterative approach
    print("Processing all rows with improved iterative Wikipedia search...")
    print("- Avoids generic keywords like 'Korea', 'Korean culture'")
    print("- Limits to 3 attempts per row")
    print("- Leaves blank if no specific keyword found")
    enhancer.enhance_csv('VQA.csv', 'VQA_enhanced_final.csv')

if __name__ == "__main__":
    main()
