#!/usr/bin/env python3

import csv
import wikipediaapi
import torch
from transformers import <PERSON>Token<PERSON>, AutoModelForCausalLM
import re
import time
from typing import Tuple, Optional

class LLMVQAEnhancer:
    def __init__(self):
        """Initialize the LLM-based VQA enhancer."""
        print("Initializing LLM VQA Enhancer...")
        
        # Initialize Wikipedia API
        self.wiki = wikipediaapi.Wikipedia(
            language='en',
            extract_format=wikipediaapi.ExtractFormat.WIKI,
            user_agent='VQAEnhancer/1.0 (<EMAIL>)'
        )
        
        # Initialize LLM
        self.model = None
        self.tokenizer = None
        self.use_llm = False
        
        # Try to load LLM models in order of preference
        model_names = [
            "meta-llama/Meta-Llama-3.1-8B-Instruct",
            "meta-llama/Llama-3.1-8B-Instruct", 
            "microsoft/DialoGPT-medium",
            "gpt2"
        ]
        
        for model_name in model_names:
            try:
                print(f"Trying to load: {model_name}")
                self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_name,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                    device_map="auto" if torch.cuda.is_available() else None,
                    trust_remote_code=True
                )
                print(f"Successfully loaded: {model_name}")
                self.use_llm = True
                break
            except Exception as e:
                print(f"Failed to load {model_name}: {e}")
                continue
        
        if not self.use_llm:
            print("Warning: Could not load any language model. Using fallback methods.")
        
        if self.tokenizer and self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        print("LLM VQA Enhancer initialized!")

    def generate_keyword_with_detailed_prompt(self, question: str, correct_option: str, korean_keyword: str = "", previous_attempts: list = None, attempt_number: int = 1) -> str:
        """Generate a keyword using detailed, strategic prompting."""
        
        if not self.use_llm:
            return self.fallback_keyword_generation(question, correct_option, korean_keyword, attempt_number)
        
        # Build context about previous attempts
        attempt_context = ""
        if previous_attempts:
            failed_attempts = [f"'{attempt}'" for attempt in previous_attempts]
            attempt_context = f"\n\nPrevious failed attempts: {', '.join(failed_attempts)}\nGenerate something completely different."
        
        # Build Korean context
        korean_context = ""
        if korean_keyword:
            korean_context = f"\nOriginal Korean keyword: {korean_keyword}"
        
        # Different detailed prompting strategies for each attempt
        if attempt_number == 1:
            strategy_prompt = """You are an expert in Korean culture and Wikipedia. Your task is to generate the most specific English Wikipedia page title that would contain relevant background information for this Korean cultural question.

ANALYSIS STEPS:
1. Read the question and correct answer carefully
2. Identify the main subject (place, building, cultural artifact, historical period, etc.)
3. If there's a Korean keyword, determine its most likely English Wikipedia equivalent
4. Generate a specific Wikipedia page title (1-4 words) that someone would search for

REQUIREMENTS:
- Must be a specific proper noun or place name
- Avoid generic terms like "Korea", "Korean culture", "architecture", "temple", "palace"
- Focus on actual Wikipedia page titles that exist
- Prioritize specific places, buildings, institutions, historical sites, cultural artifacts

EXAMPLES of good outputs:
- "Bulguksa" (specific temple)
- "Gyeongbokgung" (specific palace)
- "Jeju Island" (specific place)
- "Namsan Tower" (specific landmark)
- "Emille Bell" (specific artifact)
- "Cheomseongdae" (specific observatory)"""

        elif attempt_number == 2:
            strategy_prompt = """You are a Korean history and culture specialist. The first attempt failed to find a Wikipedia page. Try a different approach.

ALTERNATIVE ANALYSIS:
1. Look for alternative names or English translations of Korean terms
2. Consider historical periods (Silla, Joseon, Goryeo) if mentioned
3. Identify architectural types, cultural practices, or historical events
4. Look for related concepts or broader categories
5. Consider alternative spellings or romanizations

FOCUS ON:
- Historical dynasties and periods
- Alternative names for places or buildings
- Related cultural concepts
- Broader but still specific categories
- Different romanization systems"""

        else:  # attempt_number == 3
            strategy_prompt = """This is the final attempt. Be maximally creative and specific.

FINAL STRATEGY:
1. Extract any remaining proper nouns or specific terms
2. Consider related historical figures, events, or time periods
3. Look for unique architectural features, cultural practices, or artifacts
4. Try compound terms or descriptive phrases
5. Focus on the most distinctive aspect of the question
6. Generate the most specific possible Wikipedia-searchable term

BE EXTREMELY CREATIVE AND SPECIFIC."""

        prompt = f"""{strategy_prompt}

Question: {question}
Correct Answer: {correct_option}{korean_context}{attempt_context}

Generate one specific English Wikipedia page title (1-4 words only):"""

        try:
            # Tokenize input
            inputs = self.tokenizer.encode(prompt, return_tensors="pt", truncation=True, max_length=1024)
            
            # Generate response with increasing creativity
            temperature = 0.7 + (attempt_number * 0.15)
            
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_new_tokens=25,
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract keyword using multiple methods
            keyword = self.extract_keyword_from_response(response)
            
            return keyword if keyword else ""
            
        except Exception as e:
            print(f"    Error generating keyword with LLM: {e}")
            return self.fallback_keyword_generation(question, correct_option, korean_keyword, attempt_number)

    def extract_keyword_from_response(self, response: str) -> str:
        """Extract the keyword from LLM response using multiple strategies."""
        
        # Method 1: Look for text after the last colon
        if ":" in response:
            keyword = response.split(":")[-1].strip()
            if len(keyword.split()) <= 4:
                return self.clean_keyword(keyword)
        
        # Method 2: Look for text after common prompt endings
        endings = [
            "Generate one specific English Wikipedia page title",
            "Wikipedia page title",
            "Title:",
            "Answer:",
            "Output:"
        ]
        
        for ending in endings:
            if ending in response:
                keyword = response.split(ending)[-1].strip()
                if len(keyword.split()) <= 4:
                    return self.clean_keyword(keyword)
        
        # Method 3: Take the last line if it's short and looks like a title
        lines = response.strip().split('\n')
        for line in reversed(lines):
            line = line.strip()
            if line and len(line.split()) <= 4 and not line.endswith('.'):
                return self.clean_keyword(line)
        
        # Method 4: Look for quoted text
        quoted = re.findall(r'"([^"]*)"', response)
        for quote in quoted:
            if len(quote.split()) <= 4:
                return self.clean_keyword(quote)
        
        return ""

    def clean_keyword(self, keyword: str) -> str:
        """Clean and format the keyword properly."""
        # Remove unwanted characters
        keyword = re.sub(r'[^\w\s-]', '', keyword)
        keyword = keyword.strip()
        
        # Remove common prefixes
        keyword = re.sub(r'^(the|a|an)\s+', '', keyword, flags=re.IGNORECASE)
        
        # Capitalize properly for Wikipedia titles
        if keyword:
            words = keyword.split()
            keyword = ' '.join(word.capitalize() for word in words)
        
        return keyword
