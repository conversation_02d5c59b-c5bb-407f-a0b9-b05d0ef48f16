#!/usr/bin/env python3

import csv
import wikipediaapi
import torch
from transformers import <PERSON>Token<PERSON>, AutoModelForCausalLM
import re
import time
from typing import Tuple, Optional

class LLMVQAEnhancer:
    def __init__(self):
        """Initialize the LLM-based VQA enhancer."""
        print("Initializing LLM VQA Enhancer...")

        # Initialize Wikipedia API
        self.wiki = wikipediaapi.Wikipedia(
            language='en',
            extract_format=wikipediaapi.ExtractFormat.WIKI,
            user_agent='VQAEnhancer/1.0 (<EMAIL>)'
        )

        # Initialize LLM
        self.model = None
        self.tokenizer = None
        self.use_llm = False

        # Try to load LLM models in order of preference
        model_names = [
            "meta-llama/Meta-Llama-3.1-8B-Instruct",
            "meta-llama/Llama-3.1-8B-Instruct",
            "microsoft/DialoGPT-medium",
            "gpt2"
        ]

        for model_name in model_names:
            try:
                print(f"Trying to load: {model_name}")
                self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_name,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                    device_map="auto" if torch.cuda.is_available() else None,
                    trust_remote_code=True
                )
                print(f"Successfully loaded: {model_name}")
                self.use_llm = True
                break
            except Exception as e:
                print(f"Failed to load {model_name}: {e}")
                continue

        if not self.use_llm:
            print("Warning: Could not load any language model. Using fallback methods.")

        if self.tokenizer and self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

        print("LLM VQA Enhancer initialized!")

    def generate_keyword_with_detailed_prompt(self, question: str, correct_option: str, korean_keyword: str = "", previous_attempts: list = None, attempt_number: int = 1) -> str:
        """Generate a keyword using detailed, strategic prompting."""

        if not self.use_llm:
            return self.fallback_keyword_generation(question, correct_option, korean_keyword, attempt_number)

        # Build context about previous attempts
        attempt_context = ""
        if previous_attempts:
            failed_attempts = [f"'{attempt}'" for attempt in previous_attempts]
            attempt_context = f"\n\nPrevious failed attempts: {', '.join(failed_attempts)}\nGenerate something completely different."

        # Build Korean context
        korean_context = ""
        if korean_keyword:
            korean_context = f"\nOriginal Korean keyword: {korean_keyword}"

        # Different detailed prompting strategies for each attempt
        if attempt_number == 1:
            strategy_prompt = """You are an expert in Korean culture and Wikipedia. Your task is to generate the most specific English Wikipedia page title that would contain relevant background information for this Korean cultural question.

ANALYSIS STEPS:
1. Read the question and correct answer carefully
2. Identify the main subject (place, building, cultural artifact, historical period, etc.)
3. If there's a Korean keyword, determine its most likely English Wikipedia equivalent
4. Generate a specific Wikipedia page title (1-4 words) that someone would search for

REQUIREMENTS:
- Must be a specific proper noun or place name
- Avoid generic terms like "Korea", "Korean culture", "architecture", "temple", "palace"
- Focus on actual Wikipedia page titles that exist
- Prioritize specific places, buildings, institutions, historical sites, cultural artifacts

EXAMPLES of good outputs:
- "Bulguksa" (specific temple)
- "Gyeongbokgung" (specific palace)
- "Jeju Island" (specific place)
- "Namsan Tower" (specific landmark)
- "Emille Bell" (specific artifact)
- "Cheomseongdae" (specific observatory)"""

        elif attempt_number == 2:
            strategy_prompt = """You are a Korean history and culture specialist. The first attempt failed to find a Wikipedia page. Try a different approach.

ALTERNATIVE ANALYSIS:
1. Look for alternative names or English translations of Korean terms
2. Consider historical periods (Silla, Joseon, Goryeo) if mentioned
3. Identify architectural types, cultural practices, or historical events
4. Look for related concepts or broader categories
5. Consider alternative spellings or romanizations

FOCUS ON:
- Historical dynasties and periods
- Alternative names for places or buildings
- Related cultural concepts
- Broader but still specific categories
- Different romanization systems"""

        else:  # attempt_number == 3
            strategy_prompt = """This is the final attempt. Be maximally creative and specific.

FINAL STRATEGY:
1. Extract any remaining proper nouns or specific terms
2. Consider related historical figures, events, or time periods
3. Look for unique architectural features, cultural practices, or artifacts
4. Try compound terms or descriptive phrases
5. Focus on the most distinctive aspect of the question
6. Generate the most specific possible Wikipedia-searchable term

BE EXTREMELY CREATIVE AND SPECIFIC."""

        prompt = f"""{strategy_prompt}

Question: {question}
Correct Answer: {correct_option}{korean_context}{attempt_context}

Generate ONLY the Wikipedia page title (1-4 words). Do not include explanations, quotes, or multiple options. Just the title:

Title:"""

        try:
            # Tokenize input and move to correct device
            inputs = self.tokenizer.encode(prompt, return_tensors="pt", truncation=True, max_length=1024)
            if torch.cuda.is_available():
                inputs = inputs.to('cuda')

            # Generate response with increasing creativity
            temperature = 0.7 + (attempt_number * 0.15)

            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_new_tokens=25,
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    attention_mask=torch.ones_like(inputs)
                )

            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

            # Extract keyword using multiple methods
            keyword = self.extract_keyword_from_response(response)

            return keyword if keyword else ""

        except Exception as e:
            print(f"    Error generating keyword with LLM: {e}")
            return self.fallback_keyword_generation(question, correct_option, korean_keyword, attempt_number)

    def extract_keyword_from_response(self, response: str) -> str:
        """Extract the keyword from LLM response using multiple strategies."""

        # Debug: print the response to see what we're getting
        print(f"      LLM Response: {response[-200:]}")  # Last 200 chars

        # Method 1: Look for quoted text first (most reliable)
        quoted = re.findall(r'"([^"]*)"', response)
        for quote in quoted:
            quote = quote.strip()
            if (quote and
                len(quote.split()) <= 4 and
                not any(word in quote.lower() for word in ['generate', 'wikipedia', 'title', 'page', 'specific', 'english', 'attempt', 'strategy', 'korean', 'keyword'])):
                return self.clean_keyword(quote)

        # Method 2: Look specifically for "Title: X" pattern (most reliable)
        title_pattern = r'Title:\s*([^\n\r]+)'
        title_matches = re.findall(title_pattern, response, re.IGNORECASE)

        for match in title_matches:
            # Clean the match - remove everything after the first sentence or explanation
            match = match.strip().strip('"').strip()

            # Take only the first part before any explanation
            if '(' in match:
                match = match.split('(')[0].strip()
            if '\n' in match:
                match = match.split('\n')[0].strip()
            if '.' in match and len(match.split('.')) > 1:
                # If there's a period, take only the first part
                first_part = match.split('.')[0].strip()
                if len(first_part.split()) <= 4:
                    match = first_part

            # Remove common trailing words
            match = re.sub(r'\s+(museum|article|page|wikipedia)$', '', match, flags=re.IGNORECASE)

            if (match and
                len(match.split()) <= 4 and
                not any(word in match.lower() for word in ['generate', 'wikipedia', 'title', 'page', 'specific', 'english', 'attempt', 'strategy', 'explanation', 'reasoning', 'note', 'however', 'final', 'answer'])):
                return self.clean_keyword(match)

        # Method 3: Look for capitalized phrases that look like proper nouns
        # Focus on the last part of the response where the answer should be
        last_part = response[-300:]  # Last 300 characters
        proper_noun_patterns = [
            r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+){0,3})\b',  # Capitalized words (1-4 words)
        ]

        for pattern in proper_noun_patterns:
            matches = re.findall(pattern, last_part)
            for match in reversed(matches):  # Take the last ones
                if (len(match.split()) <= 4 and
                    match.lower() not in ['generate', 'wikipedia', 'title', 'page', 'specific', 'english', 'korean', 'culture', 'answer', 'result', 'output'] and
                    not any(word in match.lower() for word in ['generate', 'wikipedia', 'title', 'page', 'specific', 'english', 'attempt', 'strategy', 'korean', 'keyword'])):
                    return self.clean_keyword(match)

        # Method 4: Look for the last meaningful line
        lines = response.strip().split('\n')
        for line in reversed(lines):
            line = line.strip().strip('"').strip()
            if (line and
                len(line.split()) <= 4 and
                not line.endswith('.') and
                not line.endswith('?') and
                not line.endswith(':') and
                not any(word in line.lower() for word in ['generate', 'wikipedia', 'title', 'page', 'specific', 'english', 'attempt', 'strategy', 'korean', 'keyword', 'or', 'and', 'the', 'a', 'an'])):
                return self.clean_keyword(line)

        return ""

    def clean_keyword(self, keyword: str) -> str:
        """Clean and format the keyword properly."""
        # Remove unwanted characters
        keyword = re.sub(r'[^\w\s-]', '', keyword)
        keyword = keyword.strip()

        # Remove common prefixes
        keyword = re.sub(r'^(the|a|an)\s+', '', keyword, flags=re.IGNORECASE)

        # Capitalize properly for Wikipedia titles
        if keyword:
            words = keyword.split()
            keyword = ' '.join(word.capitalize() for word in words)

        return keyword

    def fallback_keyword_generation(self, question: str, correct_option: str, korean_keyword: str, attempt_number: int) -> str:
        """Fallback keyword generation when LLM is not available."""

        text = f"{question} {correct_option}"

        if attempt_number == 1:
            # Look for proper nouns in the text
            proper_nouns = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', text)
            filtered = [noun for noun in proper_nouns if noun.lower() not in
                       {'the', 'this', 'that', 'what', 'which', 'how', 'why', 'when', 'where', 'it'}]
            if filtered:
                return filtered[0]

        elif attempt_number == 2:
            # Look for historical/cultural terms
            cultural_terms = re.findall(r'\b(?:Silla|Joseon|Goryeo|dynasty|period)\b', text, re.IGNORECASE)
            if cultural_terms:
                return cultural_terms[0].capitalize()

            # Try specific cultural artifacts
            if 'bell' in text.lower():
                return "Emille Bell"
            elif 'bridge' in text.lower() and 'silla' in text.lower():
                return "Woljeonggyo"

        elif attempt_number == 3:
            # Look for specific place names
            places = re.findall(r'\b(?:Seoul|Busan|Gyeongju|Jeju)\b', text, re.IGNORECASE)
            if places:
                return places[0].capitalize()

            # Look for architectural terms as last resort
            arch_terms = re.findall(r'\b(?:temple|palace|shrine|fortress|observatory|tower|gate)\b', text, re.IGNORECASE)
            if arch_terms:
                return arch_terms[0].capitalize()

        return ""

    def smart_korean_translation(self, korean_keyword: str, context: str) -> str:
        """Smart translation of Korean keywords based on context."""

        context_lower = context.lower()

        # Use context clues to determine the best translation
        if '제주' in korean_keyword:
            return "Jeju Island"
        elif '서울' in korean_keyword or '남산' in korean_keyword:
            if 'tower' in context_lower:
                return "Namsan Tower"
            return "Seoul"
        elif '경복궁' in korean_keyword:
            return "Gyeongbokgung"
        elif '창덕궁' in korean_keyword:
            return "Changdeokgung"
        elif '덕수궁' in korean_keyword:
            return "Deoksugung"
        elif '운현궁' in korean_keyword:
            return "Unhyeongung"
        elif '불국사' in korean_keyword:
            return "Bulguksa"
        elif '종묘' in korean_keyword:
            return "Jongmyo"
        elif '명동' in korean_keyword:
            return "Myeongdong"
        elif '월정교' in korean_keyword:
            return "Woljeonggyo"
        elif '신라' in korean_keyword:
            if 'bell' in context_lower:
                return "Divine Bell of King Seongdeok"
            return "Silla"
        elif '조선' in korean_keyword:
            return "Joseon"
        elif '고려' in korean_keyword:
            if 'university' in context_lower:
                return "Korea University"
            return "Goryeo"
        elif '첨성대' in korean_keyword:
            return "Cheomseongdae"
        elif '수원' in korean_keyword and '화성' in korean_keyword:
            return "Hwaseong Fortress"
        elif '독립문' in korean_keyword:
            return "Independence Gate"
        elif '탑골' in korean_keyword:
            return "Tapgol Park"
        elif '광화문' in korean_keyword:
            return "Gwanghwamun"
        elif '한강' in korean_keyword:
            return "Han River"

        return ""

    def search_wikipedia(self, keyword: str) -> Tuple[Optional[str], Optional[str]]:
        """Search Wikipedia and return first paragraph and URL."""
        try:
            print(f"  Searching Wikipedia for: {keyword}")

            page = self.wiki.page(keyword)

            if page.exists():
                summary = page.summary
                if summary and len(summary.strip()) > 20:
                    first_paragraph = summary.split('\n\n')[0]
                    if len(first_paragraph) > 500:
                        sentences = first_paragraph.split('. ')
                        first_paragraph = '. '.join(sentences[:3]) + '.'

                    return first_paragraph, page.fullurl

            return None, None

        except Exception as e:
            print(f"  Error searching Wikipedia for '{keyword}': {e}")
            return None, None

    def is_relevant_content(self, content: str, question: str, correct_option: str) -> bool:
        """Check if the Wikipedia content is relevant to the question."""
        content_lower = content.lower()

        # Check for Korean-related terms
        korean_terms = ['korea', 'korean', 'seoul', 'silla', 'joseon', 'jeju', 'busan', 'gyeongju']
        has_korean_context = any(term in content_lower for term in korean_terms)

        # Check if it's a disambiguation page
        is_disambiguation = 'may refer to:' in content_lower or len(content.strip()) < 50

        return has_korean_context and not is_disambiguation

    def is_specific_enough_keyword(self, keyword: str) -> bool:
        """Check if a keyword is specific enough (not too generic)."""
        generic_keywords = {
            "korea", "korean", "korean culture", "korean architecture",
            "korean tradition", "korean history", "korean art", "culture",
            "architecture", "tradition", "history", "art", "building",
            "structure", "design", "style", "temple", "palace", "bridge"
        }
        return keyword.lower().strip() not in generic_keywords and len(keyword.strip()) > 0

    def search_wikipedia_iterative(self, question: str, correct_option: str, korean_keyword: str = "") -> Tuple[str, str, str]:
        """Search Wikipedia iteratively using LLM-generated keywords."""

        attempted_keywords = []
        max_attempts = 3

        for attempt in range(max_attempts):
            # Generate keyword using detailed prompting
            keyword = self.generate_keyword_with_detailed_prompt(
                question, correct_option, korean_keyword, attempted_keywords, attempt + 1
            )

            # Skip if keyword is not specific enough or already attempted
            if not self.is_specific_enough_keyword(keyword) or keyword in attempted_keywords or not keyword.strip():
                print(f"    Attempt {attempt + 1}: Generated keyword '{keyword}' - not specific enough or already tried")
                continue

            print(f"    Attempt {attempt + 1}: Trying keyword '{keyword}'")
            attempted_keywords.append(keyword)

            knowledge_point, source_url = self.search_wikipedia(keyword)
            if knowledge_point and self.is_relevant_content(knowledge_point, question, correct_option):
                return keyword, knowledge_point, source_url
            elif knowledge_point:
                print(f"      Found page but content not relevant, continuing search...")

        # If all attempts fail, return empty values
        print(f"    No specific enough keyword found after {max_attempts} attempts, leaving blank")
        return "", "", ""

    def process_row(self, row: dict) -> Tuple[str, str, str]:
        """Process a single row and return keyword, knowledge point, and source."""
        question = row['Question']

        # Get correct option text
        correct_option_num = int(row['Correct Option'])
        correct_option = row[f'Option {correct_option_num}']

        # Get existing Korean keyword if available
        korean_keyword = row.get('Keyword/Concept', '')

        print(f"Processing: {question[:50]}...")
        print(f"  Korean keyword: {korean_keyword}")

        # Use iterative LLM-based search
        keyword, knowledge_point, source_url = self.search_wikipedia_iterative(
            question, correct_option, korean_keyword
        )

        print(f"  Final keyword: {keyword if keyword else '(blank)'}")
        print(f"  Found knowledge point: {knowledge_point[:100] if knowledge_point else '(blank)'}...")

        return keyword, knowledge_point, source_url

    def enhance_csv(self, input_file: str, output_file: str = None, max_rows: int = None):
        """Enhance the CSV file with new columns."""
        if output_file is None:
            output_file = input_file.replace('.csv', '_enhanced.csv')

        print(f"Reading CSV file: {input_file}")

        # Read CSV file
        rows = []
        with open(input_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            fieldnames = reader.fieldnames
            for idx, row in enumerate(reader):
                rows.append(row)
                if max_rows and idx + 1 >= max_rows:
                    break

        total_rows = len(rows)
        print(f"Found {total_rows} rows to process")

        # Process each row
        for idx, row in enumerate(rows):
            try:
                keyword, knowledge_point, source = self.process_row(row)
                row['keyword'] = keyword
                row['knowledge point'] = knowledge_point
                row['knowledge point source'] = source

                print(f"Completed row {idx + 1}/{total_rows}")

                # Add small delay to avoid overwhelming APIs
                time.sleep(0.5)

            except Exception as e:
                print(f"Error processing row {idx + 1}: {e}")
                row['keyword'] = ""
                row['knowledge point'] = ""
                row['knowledge point source'] = ""

        # Save enhanced CSV
        print(f"Saving enhanced CSV to: {output_file}")
        new_fieldnames = list(fieldnames) + ['keyword', 'knowledge point', 'knowledge point source']

        with open(output_file, 'w', encoding='utf-8', newline='') as file:
            writer = csv.DictWriter(file, fieldnames=new_fieldnames)
            writer.writeheader()
            writer.writerows(rows)

        print("Enhancement complete!")

def main():
    """Main function to run the LLM-based VQA enhancement."""
    enhancer = LLMVQAEnhancer()

    # Test with first 3 rows to verify the LLM-based approach
    print("Testing LLM-based approach with first 3 rows...")
    print("- Uses detailed prompting strategies")
    print("- No hard-coded translations")
    print("- Avoids generic keywords")
    print("- Limits to 3 attempts per row")
    enhancer.enhance_csv('VQA.csv', 'VQA_enhanced_llm_test.csv', max_rows=3)

if __name__ == "__main__":
    main()
