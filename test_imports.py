#!/usr/bin/env python3

print("Testing imports...")

try:
    import pandas as pd
    print("✓ pandas imported successfully")
except ImportError as e:
    print(f"✗ pandas import failed: {e}")

try:
    import wikipediaapi
    print("✓ wikipediaapi imported successfully")
except ImportError as e:
    print(f"✗ wikipediaapi import failed: {e}")

try:
    import torch
    print("✓ torch imported successfully")
    print(f"  Torch version: {torch.__version__}")
    print(f"  CUDA available: {torch.cuda.is_available()}")
except ImportError as e:
    print(f"✗ torch import failed: {e}")

try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    print("✓ transformers imported successfully")
except ImportError as e:
    print(f"✗ transformers import failed: {e}")

print("\nTesting Wikipedia API...")
try:
    wiki = wikipediaapi.Wikipedia(
        language='en',
        extract_format=wikipediaapi.ExtractFormat.WIKI,
        user_agent='VQAEnhancer/1.0 (<EMAIL>)'
    )
    page = wiki.page('Korea')
    print(f"✓ Wikipedia page 'Korea' exists: {page.exists()}")
    if page.exists():
        summary = page.summary[:200]
        print(f"  Summary preview: {summary}...")
except Exception as e:
    print(f"✗ Wikipedia API test failed: {e}")

print("\nAll tests completed!")
