#!/usr/bin/env python3

import csv
import wikipediaapi
import re
import time
from typing import Tuple, Optional

class SimpleVQAEnhancer:
    def __init__(self):
        """Initialize the simple VQA enhancer with Wikipedia API only."""
        print("Initializing Simple VQA Enhancer...")

        # Initialize Wikipedia API
        self.wiki = wikipediaapi.Wikipedia(
            language='en',
            extract_format=wikipediaapi.ExtractFormat.WIKI,
            user_agent='VQAEnhancer/1.0 (<EMAIL>)'
        )
        print("Wikipedia API initialized!")

    def extract_keywords_from_text(self, question: str, correct_option: str, existing_keyword: str = "") -> str:
        """Extract keywords from question and correct option using simple text processing."""
        # Combine question and correct option
        text = f"{question} {correct_option}"

        # First, check if we have an existing Korean keyword that might be useful
        if existing_keyword and existing_keyword != "":
            # Try to translate or find English equivalent of Korean terms
            korean_to_english = {
                '제주': 'Jeju Island',
                '돌집': 'Jeju stone house',
                '월정교': 'Woljeong Bridge',
                '운현궁': 'Unhyeongung Palace',
                '명동': 'Myeongdong',
                '남산타워': 'Namsan Tower',
                '신라대종': 'Divine Bell of King Seongdeok',
                '고려대학교': 'Korea University',
                '한강다리': 'Han River',
                '탑골공원': 'Tapgol Park',
                '광화문': 'Gwanghwamun',
                '경복궁': 'Gyeongbokgung',
                '창덕궁': 'Changdeokgung',
                '덕수궁': 'Deoksugung',
                '종묘': 'Jongmyo',
                '독립문': 'Independence Gate',
                '불국사': 'Bulguksa',
                '첨성대': 'Cheomseongdae',
                '수원화성': 'Hwaseong Fortress',
                '해동용궁사': 'Haedong Yonggungsa'
            }

            for korean, english in korean_to_english.items():
                if korean in existing_keyword:
                    return english

        # Remove common words
        common_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'this', 'that', 'these', 'those', 'it', 'its', 'how', 'what', 'why', 'when', 'where',
            'which', 'who', 'does', 'did', 'do', 'is', 'are', 'was', 'were', 'been', 'being', 'have',
            'has', 'had', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'building',
            'structure', 'design', 'style', 'features', 'elements', 'aspects', 'factors'
        }

        # Look for specific Korean cultural terms first
        korean_terms = re.findall(r'\b(?:Korean|Korea|Silla|Joseon|Seoul|Busan|Gyeongju|palace|temple|gate|tower|bridge|market|village|fortress|shrine|observatory)\b', text, re.IGNORECASE)

        # Extract other potential keywords (capitalized words, longer words)
        words = re.findall(r'\b[A-Z][a-z]+\b|\b[a-z]{5,}\b', text)
        keywords = [word for word in words if word.lower() not in common_words]

        # Combine and prioritize
        all_keywords = korean_terms + keywords

        # Return the most relevant keywords
        if korean_terms:
            return ' '.join(korean_terms[:2])
        elif keywords:
            return ' '.join(keywords[:2])
        else:
            return "Korean culture"

    def search_wikipedia(self, keyword: str) -> Tuple[Optional[str], Optional[str]]:
        """Search Wikipedia and return first paragraph and URL."""
        try:
            print(f"  Searching Wikipedia for: {keyword}")

            # Try exact match first
            page = self.wiki.page(keyword)

            # Check if page exists with timeout handling
            try:
                exists = page.exists()
                print(f"    Page exists: {exists}")
            except Exception as e:
                print(f"    Error checking page existence: {e}")
                exists = False

            if exists:
                try:
                    summary = page.summary
                    if summary:
                        # Get first paragraph
                        first_paragraph = summary.split('\n\n')[0]
                        if len(first_paragraph) > 500:
                            # If too long, take first 2-3 sentences
                            sentences = first_paragraph.split('. ')
                            first_paragraph = '. '.join(sentences[:3]) + '.'

                        return first_paragraph, page.fullurl
                except Exception as e:
                    print(f"    Error getting summary: {e}")

            # If exact match doesn't work, try with modified keywords
            # Try individual words from the keyword
            words = keyword.split()
            for word in words:
                if len(word) > 3:  # Skip short words
                    try:
                        print(f"    Trying word: {word}")
                        page = self.wiki.page(word)
                        if page.exists():
                            summary = page.summary
                            if summary:
                                first_paragraph = summary.split('\n\n')[0]
                                if len(first_paragraph) > 500:
                                    sentences = first_paragraph.split('. ')
                                    first_paragraph = '. '.join(sentences[:3]) + '.'
                                return first_paragraph, page.fullurl
                    except Exception as e:
                        print(f"    Error with word '{word}': {e}")
                        continue

            return None, None

        except Exception as e:
            print(f"  Error searching Wikipedia for '{keyword}': {e}")
            return None, None

    def process_row(self, row: dict) -> Tuple[str, str, str]:
        """Process a single row and return keyword, knowledge point, and source."""
        question = row['Question']

        # Get correct option text
        correct_option_num = int(row['Correct Option'])
        correct_option = row[f'Option {correct_option_num}']

        # Get existing Korean keyword if available
        existing_keyword = row.get('Keyword/Concept', '')

        print(f"Processing: {question[:50]}...")
        print(f"  Existing keyword: {existing_keyword}")

        # Extract keywords using simple text processing
        keyword = self.extract_keywords_from_text(question, correct_option, existing_keyword)
        print(f"  Generated keyword: {keyword}")

        # Search Wikipedia
        knowledge_point, source_url = self.search_wikipedia(keyword)

        if knowledge_point is None:
            # Fallback: try with a simpler keyword
            simple_keyword = keyword.split()[0] if keyword else "Korea"
            print(f"  Trying fallback keyword: {simple_keyword}")
            knowledge_point, source_url = self.search_wikipedia(simple_keyword)

        if knowledge_point is None:
            knowledge_point = "No relevant Wikipedia information found."
            source_url = "N/A"

        print(f"  Found knowledge point: {knowledge_point[:100]}...")

        return keyword, knowledge_point, source_url

    def enhance_csv(self, input_file: str, output_file: str = None, max_rows: int = None):
        """Enhance the CSV file with new columns."""
        if output_file is None:
            output_file = input_file.replace('.csv', '_enhanced.csv')

        print(f"Reading CSV file: {input_file}")

        # Read CSV file
        rows = []
        with open(input_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            fieldnames = reader.fieldnames
            for idx, row in enumerate(reader):
                rows.append(row)
                if max_rows and idx + 1 >= max_rows:
                    break

        total_rows = len(rows)
        print(f"Found {total_rows} rows to process")

        # Process each row
        for idx, row in enumerate(rows):
            try:
                keyword, knowledge_point, source = self.process_row(row)
                row['keyword'] = keyword
                row['knowledge point'] = knowledge_point
                row['knowledge point source'] = source

                print(f"Completed row {idx + 1}/{total_rows}")

                # Add small delay to avoid overwhelming APIs
                time.sleep(0.5)

            except Exception as e:
                print(f"Error processing row {idx + 1}: {e}")
                row['keyword'] = "Error"
                row['knowledge point'] = "Error processing this row"
                row['knowledge point source'] = "N/A"

        # Save enhanced CSV
        print(f"Saving enhanced CSV to: {output_file}")
        new_fieldnames = list(fieldnames) + ['keyword', 'knowledge point', 'knowledge point source']

        with open(output_file, 'w', encoding='utf-8', newline='') as file:
            writer = csv.DictWriter(file, fieldnames=new_fieldnames)
            writer.writeheader()
            writer.writerows(rows)

        print("Enhancement complete!")

def main():
    """Main function to run the simple VQA enhancement."""
    enhancer = SimpleVQAEnhancer()

    # Process all rows
    print("Processing all rows...")
    enhancer.enhance_csv('VQA.csv', 'VQA_enhanced.csv')

if __name__ == "__main__":
    main()
